/*
 * UART2完整使用示例
 * 
 * 本文件展示了如何在实际项目中集成和使用UART2接收OpenMV数据的完整示例
 * 
 * 功能说明：
 * 1. 接收OpenMV发送的物体检测数据
 * 2. 根据检测结果控制设备行为
 * 3. 提供调试信息输出
 */

#include "zf_common_headfile.h"
#include "uart2_handler.h"

// 示例：在main.c中添加UART2功能的完整代码
void main_with_uart2_example(void)
{
    // 系统初始化
    clock_init(SYSTEM_CLOCK_80M);   // 时钟配置及系统初始化
    debug_init();                   // 调试串口信息初始化
    
    // 初始化UART2用于接收OpenMV数据
    uart2_init_with_interrupt();
    
    // 其他硬件初始化...
    // tft180_init();
    // gpio_init(...);
    // pwm_init(...);
    
    printf("UART2 OpenMV receiver initialized\n");
    
    // 主循环
    while(1)
    {
        // 检查OpenMV检测结果
        process_openmv_data();
        
        // 其他主循环任务...
        // menu_main();
        // 延时或其他处理
        system_delay_ms(10);
    }
}

// 处理OpenMV数据的函数
void process_openmv_data(void)
{
    static int last_detection_flag = -1;  // 记录上次的检测状态
    
    // 检查检测状态是否发生变化
    if(detection_flag != last_detection_flag)
    {
        last_detection_flag = detection_flag;
        
        if(detection_flag == 1)
        {
            // 检测到物体
            printf("Object detected at position: (%d, %d)\n", center_x, center_y);
            
            // 根据物体位置进行控制
            control_based_on_position(center_x, center_y);
        }
        else
        {
            // 未检测到物体
            printf("No object detected\n");
            
            // 执行搜索或待机逻辑
            search_mode();
        }
    }
}

// 根据物体位置进行控制的示例函数
void control_based_on_position(int x, int y)
{
    // 假设摄像头分辨率为160x120（根据你的OpenMV代码）
    const int CAMERA_WIDTH = 160;
    const int CAMERA_HEIGHT = 120;
    const int CENTER_X = CAMERA_WIDTH / 2;   // 80
    const int CENTER_Y = CAMERA_HEIGHT / 2;  // 60
    
    // 计算偏移量
    int offset_x = x - CENTER_X;
    int offset_y = y - CENTER_Y;
    
    printf("Offset from center: X=%d, Y=%d\n", offset_x, offset_y);
    
    // 根据偏移量控制设备
    if(abs(offset_x) < 10 && abs(offset_y) < 10)
    {
        // 物体在中心区域，可以进行抓取等操作
        printf("Object centered - ready for action\n");
        target_reached_action();
    }
    else
    {
        // 需要调整位置
        if(offset_x > 10)
        {
            printf("Move right\n");
            // 控制设备向右移动
            // motor_control(MOVE_RIGHT);
        }
        else if(offset_x < -10)
        {
            printf("Move left\n");
            // 控制设备向左移动
            // motor_control(MOVE_LEFT);
        }
        
        if(offset_y > 10)
        {
            printf("Move down\n");
            // 控制设备向下移动
            // motor_control(MOVE_DOWN);
        }
        else if(offset_y < -10)
        {
            printf("Move up\n");
            // 控制设备向上移动
            // motor_control(MOVE_UP);
        }
    }
}

// 搜索模式函数
void search_mode(void)
{
    static uint32 search_counter = 0;
    search_counter++;
    
    // 每隔一段时间输出搜索状态
    if(search_counter % 100 == 0)  // 假设主循环10ms一次，这里是1秒
    {
        printf("Searching for objects...\n");
        
        // 可以在这里添加搜索逻辑
        // 例如：旋转摄像头、移动设备等
        // servo_rotate();
        // motor_search_pattern();
    }
}

// 目标到达后的动作
void target_reached_action(void)
{
    printf("Target reached - executing action\n");
    
    // 在这里添加具体的动作逻辑
    // 例如：抓取物体、拍照、记录位置等
    
    // 示例：控制舵机或其他执行器
    // servo_grab();
    // gpio_set_level(LASER, GPIO_HIGH);  // 开启激光
    
    // 延时执行动作
    system_delay_ms(1000);
    
    // gpio_set_level(LASER, GPIO_LOW);   // 关闭激光
}

// 调试函数：显示接收到的原始数据
void debug_uart2_data(void)
{
    printf("UART2 Raw Data: [0x%02X 0x%02X 0x%02X 0x%02X]\n", 
           storageU2[0], storageU2[1], storageU2[2], storageU2[3]);
    
    printf("Parsed Data: detection=%d, center_x=%d, center_y=%d, reserved=%d\n",
           detection_flag, center_x, center_y, reserved_byte);
}

// 数据有效性检查函数
bool is_data_valid(void)
{
    // 检查坐标是否在合理范围内（根据摄像头分辨率）
    if(center_x < 0 || center_x > 160 || center_y < 0 || center_y > 120)
    {
        printf("Warning: Invalid coordinates (%d, %d)\n", center_x, center_y);
        return false;
    }
    
    // 检查检测标志是否有效
    if(detection_flag != 0 && detection_flag != 1)
    {
        printf("Warning: Invalid detection flag %d\n", detection_flag);
        return false;
    }
    
    return true;
}
