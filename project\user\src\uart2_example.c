/*
 * UART2数据接收使用示例
 * 
 * 本文件展示了如何使用UART2接收数据的完整示例
 * 
 * 使用步骤：
 * 1. 在main.c中包含uart2_handler.h头文件
 * 2. 在初始化代码中调用uart2_init_with_interrupt()
 * 3. 数据会自动在中断中接收并处理
 * 4. 可以通过全局变量获取接收到的数据
 */

#include "uart2_handler.h"

// 示例：在main函数中的初始化代码
void uart2_example_init(void)
{
    // 初始化UART2并启用中断
    uart2_init_with_interrupt();
    
    // 此时UART2已经配置完成，可以接收数据
    // 数据格式：0x2D 0x13 + 4字节数据 + 0x5C 0x5B
}

// 示例：如何读取接收到的数据
void uart2_example_read_data(void)
{
    // 读取处理后的数据
    int finish_flag = flag_finish;      // 完成标志
    int classification = class;         // 分类信息
    int num = number;                   // 数字信息
    int class_result = class1;          // 分类结果
    
    // 读取原始数据
    int8 detection_flag = storageU2[0]; // 检测标志
    int8 x_offset = storageU2[1];       // x轴偏移量
    int8 distance_low = storageU2[2];   // 测距低字节
    int8 distance_high = storageU2[3];  // 测距高字节
    
    // 可以在这里添加你的数据处理逻辑
    // 例如：显示数据、控制逻辑等
}

// 示例：在主循环中检查数据
void uart2_example_main_loop(void)
{
    // 在主循环中可以检查接收到的数据
    if(flag_finish != 0)
    {
        // 有新数据到达，进行处理
        uart2_example_read_data();
        
        // 处理完成后可以清除标志（根据需要）
        // flag_finish = 0;
    }
}
