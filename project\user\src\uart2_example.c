/*
 * UART2数据接收使用示例
 * 
 * 本文件展示了如何使用UART2接收数据的完整示例
 * 
 * 使用步骤：
 * 1. 在main.c中包含uart2_handler.h头文件
 * 2. 在初始化代码中调用uart2_init_with_interrupt()
 * 3. 数据会自动在中断中接收并处理
 * 4. 可以通过全局变量获取接收到的数据
 */

#include "uart2_handler.h"

// 示例：在main函数中的初始化代码
void uart2_example_init(void)
{
    // 初始化UART2并启用中断
    uart2_init_with_interrupt();
    
    // 此时UART2已经配置完成，可以接收数据
    // 数据格式：0x2D 0x13 + 4字节数据 + 0x5C 0x5B
}

// 示例：如何读取接收到的数据
void uart2_example_read_data(void)
{
    // 读取处理后的数据
    int detected = detection_flag;      // 检测标志：1-检测到，0-未检测到
    int x_coord = center_x;             // 矩形中心X坐标
    int y_coord = center_y;             // 矩形中心Y坐标
    int reserved = reserved_byte;       // 保留字节

    // 读取原始数据
    int8 raw_detection = storageU2[0];  // 检测标志
    int8 raw_center_x = storageU2[1];   // 中心X坐标
    int8 raw_center_y = storageU2[2];   // 中心Y坐标
    int8 raw_reserved = storageU2[3];   // 保留字节

    // 可以在这里添加你的数据处理逻辑
    // 例如：显示数据、控制逻辑等
}

// 示例：在主循环中检查数据
void uart2_example_main_loop(void)
{
    // 在主循环中可以检查接收到的数据
    if(detection_flag != 0)
    {
        // 检测到物体，进行处理
        uart2_example_read_data();

        // 可以根据检测结果进行相应的控制逻辑
        // 例如：移动到目标位置 (center_x, center_y)
    }
    else
    {
        // 未检测到物体的处理逻辑
        // 例如：搜索模式、停止等待等
    }
}
