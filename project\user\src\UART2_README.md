# UART2数据接收功能说明

## 概述
本功能实现了使用UART 2进行数据接收，使用UART2_TX_B15和UART2_RX_B16引脚，在ISR串口通信中断中调用进行接收。

## 文件说明
- `uart2_handler.h` - 头文件，包含函数声明和变量声明
- `uart2_handler.c` - 源文件，包含具体实现
- `uart2_example.c` - 使用示例
- `isr.c` - 已修改，在UART2中断中调用接收处理函数

## 数据帧格式
接收的数据帧格式为8字节（与OpenMV上位机匹配）：
```
头帧1: 0x2B
头帧2: 0x11
数据1: 检测标志 (1-检测到物体, 0-未检测到)
数据2: 矩形中心X轴坐标
数据3: 矩形中心Y轴坐标
数据4: 保留字节 (通常为0)
尾帧1: 0x5A
尾帧2: 0x59
```

## 使用方法

### 1. 包含头文件
在需要使用UART2功能的文件中包含头文件：
```c
#include "uart2_handler.h"
```

### 2. 初始化UART2
在main函数或初始化函数中调用：
```c
uart2_init_with_interrupt();
```

### 3. 读取数据
可以通过以下全局变量获取接收到的数据：
```c
// 处理后的数据
extern int detection_flag;  // 检测标志：1-检测到物体，0-未检测到
extern int center_x;        // 矩形中心X轴坐标
extern int center_y;        // 矩形中心Y轴坐标
extern int reserved_byte;   // 保留字节

// 原始数据
extern int8 storageU2[4]; // 4字节原始数据
```

### 4. 示例代码
```c
#include "uart2_handler.h"

int main(void)
{
    // 系统初始化
    clock_init(SYSTEM_CLOCK_80M);
    debug_init();
    
    // 初始化UART2
    uart2_init_with_interrupt();
    
    while(1)
    {
        // 检查是否检测到物体
        if(detection_flag != 0)
        {
            // 检测到物体，处理坐标数据
            printf("Object detected at: (%d, %d)\n", center_x, center_y);

            // 根据坐标进行控制逻辑
            // 例如：移动到目标位置、抓取物体等
        }
        else
        {
            // 未检测到物体
            printf("No object detected\n");
            // 执行搜索逻辑或等待
        }

        // 其他主循环代码
    }
}
```

## 配置参数
- **串口号**: UART_2
- **波特率**: 115200
- **TX引脚**: UART2_TX_B15 (B15)
- **RX引脚**: UART2_RX_B16 (B16)
- **中断优先级**: 1

## 数据处理逻辑
接收到完整数据帧后，会自动进行以下处理：
1. 提取4字节数据部分到storageU2数组
2. 更新detection_flag（检测标志）
3. 更新center_x和center_y（物体中心坐标）
4. 更新reserved_byte（保留字节）

## 注意事项
1. 确保在调用uart2_init_with_interrupt()之前已经完成系统时钟初始化
2. 数据接收是在中断中完成的，主程序只需要读取全局变量即可
3. 如果需要修改数据帧格式，请修改uart2_rx_art()函数中的状态机
4. 中断优先级可以根据系统需求进行调整

## 故障排除
1. **无法接收数据**: 检查引脚连接和波特率设置
2. **数据错误**: 检查数据帧格式是否匹配
3. **中断不响应**: 检查中断是否正确启用和优先级设置
